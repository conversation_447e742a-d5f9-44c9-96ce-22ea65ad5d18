<template>
  <div class="art-overview-container">
    <div class="overview-content">
      <!-- 项目基本信息卡片 -->
      <!-- <div class="info-card">
        <div class="card-header">
          <h3 class="card-title">{{ $t('projectDetail.projectDetail') }}</h3>
        </div>
        <div class="card-body">
          <el-descriptions :column="1" size="small">
            <el-descriptions-item :label="$t('projectDetail.projectName')">
              <span class="info-value">{{ projectName }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('projectDetail.lastScanned')">
              <span class="info-value">{{ proStat.scan_time }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div> -->
      <!-- 数据统计区域 -->
      <div class="stats-section">
        <div class="section-header">
          <h3 class="section-title">资源统计分析</h3>
          <div class="section-actions">
            <el-tooltip :content="showChart ? '切换到表格视图' : '切换到图表视图'" placement="top">
              <el-button
                type="text"
                :icon="showChart ? 'el-icon-s-grid' : 'el-icon-pie-chart'"
                @click="toggleView"
                class="toggle-btn"
              >
                {{ showChart ? '表格' : '图表' }}
              </el-button>
            </el-tooltip>
          </div>
        </div>

        <!-- 图表视图 -->
        <div v-if="showChart" class="chart-view">
          <div class="chart-tabs">
            <el-tabs v-model="activeName" @tab-click="pieChartChange" size="small">
              <el-tab-pane :label="$t('projectDetail.totalResources')" name="type" />
              <el-tab-pane :label="$t('projectDetail.totalResourcesSize')" name="size" />
              <el-tab-pane :label="$t('projectDetail.errorCount')" name="error" />
            </el-tabs>
          </div>
          <div class="chart-content">
            <div v-if="pieChartData.length > 0" class="pie-chart-wrapper">
              <pie-chart
                :pieChartData="pieChartData"
                :title="pieChartTitle"
              ></pie-chart>
            </div>
            <div v-else class="empty-chart">
              <el-empty :image-size="120" description="暂无数据" />
            </div>
          </div>
        </div>

        <!-- 表格视图 -->
        <div v-else class="table-view">
          <div class="table-wrapper">
            <el-table
              :data="tableData"
              size="small"
              :default-sort="{ prop: 'count', order: 'descending' }"
              class="overview-table"
              style="min-height: 300px"
            >
              <el-table-column
                prop="type"
                :label="$t('projectDetail.resourceType')"
                show-overflow-tooltip
                min-width="120"
              />
              <el-table-column
                prop="count"
                :label="$t('projectDetail.total')"
                sortable
                width="100"
                align="center"
              />
              <el-table-column
                prop="size_sum"
                :label="'大小(KB)'"
                sortable
                width="120"
                align="center"
              />
              <el-table-column
                prop="error_rule_num"
                :label="$t('projectDetail.errorCount')"
                sortable
                width="100"
                align="center"
              >
                <template slot-scope="scope">
                  <el-tag
                    :type="scope.row.error_rule_num > 0 ? 'danger' : 'success'"
                    size="mini"
                  >
                    {{ scope.row.error_rule_num }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- 错误趋势图区域 -->
      <div class="trend-section">
        <div class="section-header">
          <h3 class="section-title">{{ $t("projectDetail.resourceErrorCount") }}</h3>
        </div>
        <div class="trend-chart-wrapper">
          <ErrorLineChart
            :xAxisData="scanTimeList"
            :errorChartData="errorChartData"
            :height="'300px'"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BackToTop from '@/components/BackToTop'
import ErrorLineChart from './components/ErrorLineChart.vue'
import ReportList from './components/ReportList.vue'
import { getReportStat, getReportSummary } from '@/api/project'
import { mapGetters, mapActions } from 'vuex'
import PieChart from './components/PieChart.vue'

export default {
  components: {
    BackToTop,
    ReportList,
    ErrorLineChart,
    PieChart,
  },
  name: "Prodetail",
  data() {
    return {
      activeName: 'type', // 饼状图切换数据
      showChart: true, // 控制显示图表还是表格
      proStat: {}, // 项目报告信息
      tableData: [],
      pieChartData: [],
      pieChartTitle: '',
      resourceTypeData: [], // 资源类型总数
      resourceSizeData: [], // 资源类型总大小
      resourceErrorData: [], // 资源告警数

      scanTimeList: [], // 保存报告列表的扫描时间
      errorChartData: [],
      errorChartHeigh: '',

      reportList: [],
      reportListTotal: 0
    }
  },
  // 在页面离开时记录滚动位置
  beforeRouteLeave(to, from, next) {
    this.scrollTop =
      document.documentElement.scrollTop || document.body.scrollTop
    next()
  },
  // 进入该页面时，用之前保存的滚动位置赋值
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      document.body.scrollTop = vm.scrollTop
    })
  },

  mounted() {
    this.initializeData()
  },
  watch: {
    // 监听路由变化，更新项目ID
    '$route'(to, from) {
      if (to.params.projectId !== from.params.projectId) {
        console.log('项目ID变化，重新获取数据:', to.params.projectId);
        this.initializeData();
      }
    },
    // 监听选中分支变化
    selectedBranch(newBranch, oldBranch) {
      if (newBranch !== oldBranch && newBranch && this.currentProjectId) {
        console.log('分支变化，重新获取数据:', newBranch);
        this.refreshData();
      }
    },
    // 监听选中检查类型变化
    selectedCheckType(newType, oldType) {
      if (newType !== oldType && newType && this.currentProjectId && this.selectedBranch) {
        console.log('检查类型变化，重新获取数据:', newType);
        this.refreshData();
      }
    }
  },
  computed: {
    ...mapGetters(['projectName', 'projectBranch', 'selectedBranch', 'selectedCheckType']),
    // 获取当前项目ID
    currentProjectId() {
      return this.$route.params.projectId || sessionStorage.getItem('currentProjectId')
    }
  },
  methods: {
    ...mapActions('project', ['fetchBranchOptions']),

    // 初始化数据
    async initializeData() {
      // if (!this.currentProjectId) {
      //   console.warn('未找到项目ID');
      //   return;
      // }

      // // 获取分支数据
      // try {
      //   await this.fetchBranchOptions(this.currentProjectId);
      // } catch (error) {
      //   console.error('获取分支数据失败:', error);
      // }

      // 获取报告数据
      this.refreshData();
    },

    // 刷新数据
    refreshData() {
      this.getReportStat();
      this.getTypeError();
      this.getReportList();
    },

    // 获取报告基本数据
    async getReportStat() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        return;
      }

      const params = { projectId, branch };
      const { code, data } = await getReportStat(params);
      if (code === 200) {
        this.proStat = data.branch;
        this.tableData = data.stat;
        this.resourceTypeData = [];
        this.resourceSizeData = [];
        this.resourceErrorData = [];
        data.stat.forEach((item) => {
          this.resourceTypeData.push({ value: item.count, name: item.type })
          this.resourceSizeData.push({ value: item.size_sum, name: item.type })
          this.resourceErrorData.push({
            value: item.error_rule_num,
            name: item.type
          })
        })
        this.pieChartChange()
      }
    },

    // 获取资源告警数
    async getTypeError() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        return;
      }

      const params = {
        projectId,
        branch,
        pageNum: 1,
        pageSize: 10,
      };
      const { code, data } = await getReportSummary(params);
      if (code !== 200) return;
      data.reverse(); //反转数据

      let arr = [];
      this.scanTimeList = [];
      data.forEach((item) => {
        arr.push({ type: 'ErrorTotal', count: item.errorTotal })
        arr.push(...item.typeError)
        this.scanTimeList.push(item.scan_time)
      })
      const dataArr = []
      arr.map((items) => {
        const res = dataArr.some((item) => {
          // 判断相同名字，有就添加到当前项
          if (item.type == items.type) {
            item.count.push(items.count)
            return true
          }
        })
        if (!res) {
          // 如果没找相同名字添加一个新对象
          dataArr.push({ type: items.type, count: [items.count] })
        }
      })
      this.errorChartHeigh = dataArr.length + 680 + 'px'
      this.errorChartData = dataArr
    },
    // 获取报告列表
    async getReportList(listQuery) {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        this.goBack();
        return;
      }

      const params = {
        projectId,
        branch,
        pageNum: listQuery ? listQuery.page : 1,
        pageSize: listQuery ? listQuery.limit : 10
      }
      const { code, data, dataLen } = await getReportSummary(params)
      if (code === 200) {
        this.reportList = data
        this.reportListTotal = dataLen
      }
    },

    goBack() {
    //   this.$router.push("/resourcedetect/project");
    },
    // 切换饼状图数据
    pieChartChange() {
      if (this.activeName === 'type') {
        this.pieChartData = this.resourceTypeData
        this.pieChartTitle = this.$t('projectDetail.resourceComposition')
      } else if (this.activeName === 'size') {
        this.pieChartData = this.resourceSizeData
        this.pieChartTitle = this.$t('projectDetail.resourceSize')
      } else {
        this.pieChartData = this.resourceErrorData
        this.pieChartTitle = this.$t('projectDetail.resourceErrorCount')
      }
    },

    // 切换图表和表格视图
    toggleView() {
      this.showChart = !this.showChart;
    }
  }
}
</script>

<style lang="scss" scoped>
.art-overview-container {
  height: 100%;
  overflow-y: auto;
  background-color: #f8f9fa;
}

.overview-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 100%;
}

// 信息卡片样式
.info-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 12px 16px;
  color: #fff;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.card-body {
  padding: 16px;
}

.info-value {
  color: #333;
  font-weight: 500;
}

// 统计区域样式
.stats-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.section-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  color: #fff;

}

.section-actions {
  display: flex;
  align-items: center;
}

.toggle-btn {
  padding: 4px 8px;
  font-size: 12px;
  color: #fff;

  &:hover {
    color: #409eff;
    background-color: rgba(64, 158, 255, 0.1);
  }
}

// 图表视图样式
.chart-view {
  display: flex;
  flex-direction: column;
}

.chart-tabs {
  padding: 0 16px;
  border-bottom: 1px solid #e9ecef;

  ::v-deep .el-tabs__header {
    margin: 0;
  }

  ::v-deep .el-tabs__nav-wrap::after {
    display: none;
  }
}

.chart-content {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart-wrapper {
  width: 100%;
  height: 400px;
}

.empty-chart {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 表格视图样式
.table-view {
  padding: 16px;
}

.table-wrapper {
  width: 100%;
}

// 趋势图区域样式
.trend-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.trend-chart-wrapper {
  padding: 16px;

  // 让图表容器自适应高度
  ::v-deep .echarts {
    min-height: 300px !important;
    height: auto !important;
  }
}

// 表格样式优化
.overview-table {
  border: none !important;

  ::v-deep .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #f8f9fa;
        color: #606266;
        font-weight: 500;
        font-size: 12px;
        border-bottom: 1px solid #e9ecef;
        padding: 8px 0;
      }
    }
  }

  ::v-deep .el-table__body-wrapper {
    .el-table__body {
      tr {
        &:hover {
          background-color: #f5f7fa;
        }

        td {
          font-size: 12px;
          padding: 6px 0;
          border-bottom: 1px solid #f0f0f0;
        }
      }
    }

    // 滚动条样式
    &::-webkit-scrollbar {
      height: 4px;
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f8f9fa;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d1d5db;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #9ca3af;
    }
  }
}

// 描述列表样式优化
::v-deep .el-descriptions {
  .el-descriptions__header {
    margin-bottom: 12px;
  }

  .el-descriptions__body {
    .el-descriptions__table {
      .el-descriptions__cell {
        padding-bottom: 8px;
      }

      .el-descriptions__label {
        color: #666;
        font-weight: 500;
        font-size: 13px;
      }

      .el-descriptions__content {
        color: #333;
        font-size: 13px;
      }
    }
  }
}

// 标签页样式优化
::v-deep .el-tabs {
  .el-tabs__item {
    font-size: 13px;
    padding: 0 16px;
    height: 36px;
    line-height: 36px;

    &.is-active {
      color: #409eff;
      font-weight: 500;
    }
  }

  .el-tabs__active-bar {
    height: 2px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .overview-content {
    padding: 12px;
    gap: 12px;
  }

  .pie-chart-wrapper {
    height: 300px;
  }

  .empty-chart {
    height: 300px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .toggle-btn {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .overview-content {
    padding: 8px;
    gap: 8px;
  }

  .pie-chart-wrapper {
    height: 250px;
  }

  .empty-chart {
    height: 250px;
  }
}
</style>
