<template>
  <div class="table-branch-overview">
    <div class="overview-stats">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-s-grid"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ total }}</div>
            <div class="stat-label">总检查次数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon success">
            <i class="el-icon-circle-check"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ successCount }}</div>
            <div class="stat-label">检查通过</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon error">
            <i class="el-icon-circle-close"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ errorCount }}</div>
            <div class="stat-label">检查失败</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon info">
            <i class="el-icon-info"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ ruleCount }}</div>
            <div class="stat-label">涉及规则</div>
          </div>
        </div>
      </div>
    </div>

    <div class="overview-rules">
      <h4 class="section-title">规则检查统计</h4>
      <div class="rules-list">
        <div 
          v-for="rule in ruleStats" 
          :key="rule.name"
          class="rule-item"
        >
          <div class="rule-info">
            <div class="rule-name">{{ rule.name }}</div>
            <div class="rule-count">检查次数: {{ rule.count }}</div>
          </div>
          <div class="rule-status">
            <el-tag 
              :type="rule.successRate === 100 ? 'success' : rule.successRate > 50 ? 'warning' : 'danger'"
              size="mini"
            >
              成功率: {{ rule.successRate }}%
            </el-tag>
          </div>
        </div>
        <div v-if="ruleStats.length === 0" class="no-data">
          暂无规则统计数据
        </div>
      </div>
    </div>

    <div class="overview-timeline">
      <h4 class="section-title">检查时间线</h4>
      <div class="timeline-chart">
        <el-empty 
          description="时间线图表功能开发中..." 
          :image-size="80"
        />
      </div>
    </div>

    <div class="overview-recent">
      <h4 class="section-title">最近检查记录</h4>
      <div class="recent-list">
        <div 
          v-for="report in recentReports" 
          :key="report.id"
          class="recent-item"
        >
          <div class="recent-info">
            <div class="recent-rule">{{ report.rule_name || '未知规则' }}</div>
            <div class="recent-time">{{ formatTime(report.scan_time) }}</div>
          </div>
          <div class="recent-status">
            <el-tag 
              :type="getStatusType(report.status)"
              size="mini"
            >
              {{ report.status || '未知状态' }}
            </el-tag>
          </div>
        </div>
        <div v-if="recentReports.length === 0" class="no-data">
          暂无检查记录
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "TableBranchOverview",
  props: {
    projectId: {
      type: String,
      required: true
    },
    branch: {
      type: String,
      required: true
    },
    reportsList: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    }
  },
  computed: {
    successCount() {
      return this.reportsList.filter(report => 
        this.isSuccess(report.status)
      ).length;
    },
    errorCount() {
      return this.reportsList.filter(report => 
        !this.isSuccess(report.status)
      ).length;
    },
    ruleCount() {
      const rules = new Set();
      this.reportsList.forEach(report => {
        if (report.rule_name) {
          rules.add(report.rule_name);
        }
      });
      return rules.size;
    },
    ruleStats() {
      const stats = {};
      this.reportsList.forEach(report => {
        const ruleName = report.rule_name || '未知规则';
        if (!stats[ruleName]) {
          stats[ruleName] = {
            name: ruleName,
            count: 0,
            successCount: 0
          };
        }
        stats[ruleName].count++;
        if (this.isSuccess(report.status)) {
          stats[ruleName].successCount++;
        }
      });

      return Object.values(stats).map(stat => ({
        ...stat,
        successRate: stat.count > 0 ? Math.round((stat.successCount / stat.count) * 100) : 0
      })).slice(0, 10); // 只显示前10个规则
    },
    recentReports() {
      // 显示最近8条记录
      return this.reportsList.slice(0, 8);
    }
  },
  methods: {
    isSuccess(status) {
      if (!status) return false;
      const statusLower = status.toLowerCase();
      return statusLower.includes('成功') || 
             statusLower.includes('通过') || 
             statusLower.includes('完成') ||
             statusLower.includes('success');
    },
    getStatusType(status) {
      if (!status) return 'info';
      const statusLower = status.toLowerCase();
      if (statusLower.includes('错误') || statusLower.includes('error') || statusLower.includes('失败')) {
        return 'danger';
      } else if (statusLower.includes('成功') || statusLower.includes('通过') || statusLower.includes('完成')) {
        return 'success';
      } else if (statusLower.includes('警告') || statusLower.includes('warning')) {
        return 'warning';
      } else {
        return 'info';
      }
    },
    formatTime(timeStr) {
      if (!timeStr) return "";
      const date = new Date(timeStr);
      return date.toLocaleString("zh-CN", {
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    }
  }
};
</script>

<style scoped lang="scss">
.table-branch-overview {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.overview-stats {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #666;
  font-size: 18px;

  &.success {
    background-color: #f0f9ff;
    color: #67c23a;
  }

  &.error {
    background-color: #fef0f0;
    color: #f56c6c;
  }

  &.info {
    background-color: #f4f4f5;
    color: #909399;
  }
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 13px;
  color: #666;
  margin-top: 4px;
}

.overview-rules,
.overview-timeline,
.overview-recent {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.rules-list,
.recent-list {
  max-height: 300px;
  overflow-y: auto;
}

.rule-item,
.recent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.rule-info,
.recent-info {
  flex: 1;
}

.rule-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.rule-count {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.recent-rule {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.recent-time {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.timeline-chart {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
  font-size: 14px;
}
</style>
