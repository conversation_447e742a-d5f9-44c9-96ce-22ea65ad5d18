import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

/* Router Modules */

/**
 * Note: sub-menu only appear when route children.length >= 1
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * 不受权限管理的页面路由
 */
const baseRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index"),
      },
    ],
  },
  {
    path: "/profile",
    component: Layout,
    redirect: "/profile/index",
    hidden: true,
    children: [
      {
        path: "index",
        component: () => import("@/views/profile/index"),
        name: "Profile",
        meta: { title: "profile", icon: "user", noCache: true },
      },
    ],
  },
  // {
  //   path: "/reportdetail",
  //   component: () => import("@/views/projectreport/guest-detail"),
  //   name: "GuestReportDetail",
  //   hidden: true,
  //   meta: {
  //     title: "reportDetail",
  //     noCache: true,
  //   },
  // },
  {
    path: "/404",
    component: () => import("@/views/error-page/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error-page/401"),
    hidden: true,
  },
    {
    path: "/product",
    component: () => import("@/views/product/index.vue"),
    hidden: true,
  }
];

// 登录页面路由 - 只在本地开发模式下添加
const loginRoute = {
  path: "/login",
  component: () => import("@/views/login/index"),
  hidden: true,
};

// 根据环境变量决定是否包含登录页面
export const constantRoutes = [
  ...baseRoutes,
  // 只有在本地开发模式下才添加登录页面
  ...(process.env.VUE_APP_DEPLOY_TYPE === "local" ? [loginRoute] : [])
];

/**
 * 受权限管理的页面路由
 */
export const asyncRoutes = [
  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/home',
  //   children: [
  //     {
  //       path: 'home',
  //       component: () => import('@/views/dashboard/index'),
  //       name: 'Home',
  //       meta: { title: 'home', icon: 'dashboard', affix: true }
  //     }
  //   ]
  // },
  {
    path: "/",
    component: Layout,
    redirect: "/product",
    children: [
      // {
      //   path: "home",
      //   component: () => import("@/views/dashboard/index"),
      //   name: "Home",
      //   meta: { title: "home", icon: "el-icon-s-home", affix: true },
      // },
    ],
  },
  // 项目管理 - 包含所有项目相关的页面
  {
    path: "/project/:projectId",
    component: Layout,
    redirect: to => {
      // 动态重定向到报告管理页面
      return `/project/${to.params.projectId}/report`;
    },
    alwaysShow: false, // 不强制显示父菜单，让子菜单直接显示为一级菜单
    name: "ProjectManagement",
    meta: {
      title: "项目详情",
      icon: "el-icon-folder-opened",
      roles: ["admin", "editor"],
    },
    children: [
      {
        path: "report",
        component: () => import("@/views/report/index.vue"),
        name: "ReportOverview",
        meta: {
          title: "报告管理",
          icon: "el-icon-document",
          roles: ["admin", "editor"],
        },
      },
      {
        path: "data-overview",
        component: () => import("@/views/data-overview/index.vue"),
        name: "DataOverview",
        meta: {
          title: "报告概览",
          icon: "el-icon-data-analysis",
          roles: ["admin", "editor"],
        },
      },
      {
        path: "rules",
        component: () => import("@/views/rules/index.vue"),
        name: "RulesOverview",
        meta: {
          title: "规则管理",
          icon: "el-icon-setting",
          roles: ["admin", "editor"],
        },
      },
      {
        path: "resource-check",
        component: () => import("@/views/resource-check/index.vue"),
        name: "ResourceCheckOverview",
        meta: {
          title: "资源检查",
          icon: "el-icon-search",
          roles: ["admin", "editor"],
        },
      },
      {
        path: "branch",
        component: () => import("@/views/branch/index.vue"),
        name: "BranchOverview",
        meta: {
          title: "分支管理",
          icon: "el-icon-share",
          roles: ["admin", "editor"],
        },
      },
    ],
  },






 //0722
  // {
  //   path: "/resourcedetect",
  //   component: Layout,
  //   redirect: "/resourcedetect/project",
  //   alwaysShow: true,
  //   name: "ResourceDetect",
  //   meta: {
  //     title: "resourceDetect",
  //     icon: "dashboard",
  //     roles: ["admin", "editor"],
  //   },
  //   children: [
  //     {
  //       path: "project",
  //       component: () => import("@/views/myproject/index"),
  //       name: "MyProject",
  //       meta: {
  //         title: "myProject",
  //         icon: "tree-table",
  //         roles: ["admin", "editor"],
  //       },
  //     },
  //     {
  //       path: "rulelist",
  //       component: () => import("@/views/rules-list/index"),
  //       name: "RuleList",
  //       meta: { title: "rulesList", icon: "list", roles: ["admin", "editor"] },
  //     },
  //     {
  //       path: "rulecreate",
  //       component: () => import("@/views/rule-create/index"),
  //       name: "RuleCreate",
  //       meta: { title: "ruleCreate", icon: "edit", roles: ["admin", "editor"] },
  //     },
  //   ],
  // },
  //0722
  // {
  //   path: '/project',
  //   component: Layout,
  //   redirect: '/project/index',
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/myproject/index'),
  //       name: 'Myproject',
  //       meta: { title: 'myProject', icon: 'tree-table', roles: ['admin', 'editor'] },
  //     },
  //   ]
  // },

  // 项目详情 注释0722
  // {
  //   path: "/prodetail",
  //   component: Layout,
  //   redirect: "/prodetail/report",
  //   hidden: true,
  //   children: [
  //     {
  //       path: "report",
  //       component: () => import("@/views/projectdetail/index"),
  //       name: "Reportdetail",
  //       meta: {
  //         parent: { name: "MyProject" },
  //         title: "projectReport",
  //         roles: ["admin", "editor"],
  //         activeMenu: "/resourcedetect/project",
  //       },
  //     },
  //     {
  //       path: "reportdetail",
  //       component: () => import("@/views/projectreport/detail"),
  //       name: "ReportDetail",
  //       meta: {
  //         parent: { name: "Reportdetail" },
  //         title: "reportDetail",
  //         roles: ["admin", "editor"],
  //         noCache: true,
  //         activeMenu: "/resourcedetect/project",
  //       },
  //     },
  //     {
  //       path: "reportcomparison",
  //       component: () => import("@/views/projectreport/report-comparison"),
  //       name: "Comparison",
  //       meta: {
  //         parent: { name: "Reportdetail" },
  //         title: "reportComparison",
  //         roles: ["admin", "editor"],
  //         noCache: true,
  //         activeMenu: "/resourcedetect/project",
  //       },
  //     },
  //   ],
  // },
  // {
  //   path: "/ruleedit",
  //   component: Layout,
  //   redirect: "/ruleedit/checkrule",
  //   hidden: true,
  //   children: [
  //     {
  //       path: "checkrule",
  //       component: () => import("@/views/rule-edit/index"),
  //       name: "Ruleedit",
  //       meta: {
  //         parent: { name: "RuleList" },
  //         title: "ruleEdit",
  //         roles: ["admin", "editor"],
  //         noCache: true,
  //         activeMenu: "/resourcedetect/rulelist",
  //       },
  //     },
  //   ],
  // },

  // {
  //   path: "/tabledetect",
  //   component: Layout,
  //   redirect: "/tabledetect/project",
  //   alwaysShow: true,
  //   name: "TableDetect",
  //   meta: {
  //     title: "tableDetect",
  //     icon: "table",
  //   },
  //   children: [
  //     {
  //       path: "project",
  //       component: () => import("@/views/table-detect/myproject"),
  //       name: "TableProject",
  //       meta: { title: "myProject", icon: "tree-table" },
  //     },
  //     {
  //       path: "rulelist",
  //       component: () => import("@/views/table-detect/rule-list"),
  //       name: "TableRuleList",
  //       meta: { title: "rulesList", icon: "list" },
  //     },
  //     {
  //       path: "rulereport",
  //       component: () => import("@/views/table-detect/report-details/index"),
  //       name: "RuleReport",
  //       hidden: true,
  //       meta: {
  //         parent: { name: "TableRuleList" },
  //         title: "ruleReport",
  //         roles: ["admin", "editor"],
  //         noCache: true,
  //         activeMenu: "/tabledetect/rulelist",
  //       },
  //     },
  //     {
  //       path: "rulecreate",
  //       component: () => import("@/views/table-detect/rule-create"),
  //       name: "TableRuleCreate",
  //       meta: { title: "ruleCreate", icon: "edit" },
  //     },
  //   ],
  // },
  // {
  //   path: "/tabledetail",
  //   component: Layout,
  //   redirect: "/tabledetail/report",
  //   hidden: true,
  //   children: [
  //     {
  //       path: "report",
  //       component: () => import("@/views/table-detect/project-details/index"),
  //       name: "TableDetail",
  //       meta: {
  //         parent: { name: "TableProject" },
  //         title: "tableProjectReport",
  //         roles: ["admin", "editor"],
  //         activeMenu: "/tabledetect/project",
  //       },
  //     },
  //     // {
  //     //   path: "checkdetail",
  //     //   component: () =>
  //     //     import("@/views/table-detect/project-details/check-details"),
  //     //   name: "CheckDetail",
  //     //   meta: {
  //     //     parent: { name: "TableDetail" },
  //     //     title: "reportDetail",
  //     //     roles: ["admin", "editor"],
  //     //     activeMenu: "/tabledetect/project",
  //     //   },
  //     // },
  //     // {
  //     //   path: "reportdetail",
  //     //   component: () => import("@/views/table-detect/report-details/index"),
  //     //   name: "Detail",
  //     //   meta: {
  //     //     parent: { name: "TableDetail" },
  //     //     title: "reportDetail",
  //     //     roles: ["admin", "editor"],
  //     //     noCache: true,
  //     //     activeMenu: "/tabledetect/rulelist",
  //     //   },
  //     // },
  //   ],
  // },
  // {
  //   path: "/tableruleedit",
  //   component: Layout,
  //   redirect: "/tableruleedit/editrule",
  //   hidden: true,
  //   children: [
  //     {
  //       path: "editrule",
  //       component: () => import("@/views/table-detect/rule-edit/index"),
  //       name: "RuleEdit",
  //       meta: {
  //         parent: { name: "TableRuleList" },
  //         title: "tableRuleEdit",
  //         roles: ["admin", "editor"],
  //         noCache: true,
  //         activeMenu: "/tabledetect/rulelist",
  //       },
  //     },
  //   ],
  // },
  //注释0722
  // {
  //   path: '/rulelist',
  //   component: Layout,
  //   redirect: '/rulelist/index',
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/rules-list/index'),
  //       name: 'RuleList',
  //       meta: { title: 'rulesList', icon: 'list', roles: ['admin', 'editor'] }
  //     }
  //   ]
  // },
  // {
  //   path: '/rulecreate',
  //   component: Layout,
  //   redirect: '/rulecreate/index',
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/rule-create/index'),
  //       name: 'RuleCreate',
  //       meta: { title: 'ruleCreate', icon: 'edit', roles: ['admin', 'editor'] }
  //     }
  //   ]
  // },

  {
    path: "/permission",
    component: Layout,
    redirect: "/permission/role",
    alwaysShow: true, // will always show the root menu
    name: "Permission",
    meta: {
      title: "permission",
      icon: "lock",
      roles: ["admin", "editor"], // you can set roles in root nav
    },
    children: [
      {
        path: "role",
        component: () => import("@/views/permission/role"),
        name: "PermissionRole",
        meta: {
          title: "roleManagement",
          icon: "el-icon-key",
          roles: ["admin"],
        },
      },
      {
        path: "users",
        component: () => import("@/views/permission/users"),
        name: "PermissionUsers",
        meta: {
          title: "userManagement",
          icon: "peoples",
          roles: ["admin"],
        },
      },
      // {
      //   path: "resourceproject",
      //   component: () => import("@/views/permission/project"),
      //   name: "PermissionResourceProject",
      //   meta: {
      //     title: "projectManagement",
      //     icon: "el-icon-files",
      //     roles: ["admin"],
      //   },
      // },
      // {
      //   path: "tableproject",
      //   component: () => import("@/views/permission/table-project"),
      //   name: "PermissionTableProject",
      //   meta: {
      //     title: "tableManagement",
      //     icon: "el-icon-files",
      //     roles: ["admin"],
      //   },
      // },
    ],
  },

  // {
  //   path: '/helpguide',
  //   component: Layout,
  //   redirect: '/helpguide/guide1',
  //   alwaysShow: true,
  //   name: 'HelpGuide',
  //   meta: {
  //     title: 'helpGuide',
  //     icon: 'education',
  //     roles: ['admin', 'editor']
  //   },
  //   children: [
  //     {
  //       path: 'guide1',
  //       component: () => import('@/views/help-guide/guide01'),
  //       name: 'Guide1',
  //       meta: {
  //         title: 'helpGuide01',
  //       }
  //     },
  //     {
  //       path: 'guide2',
  //       component: () => import('@/views/help-guide/guide02'),
  //       name: 'Guide2',
  //       meta: {
  //         title: 'helpGuide02',
  //       }
  //     },
  //   ]
  // },

  // 404 page must be placed at the end !!!
  { path: "*", redirect: "/404", hidden: true },
];

const createRouter = () =>
  new Router({
    mode: "hash", // hash mode doesn't require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
