<template>
  <div class="rules-container">
    <!-- 左右布局 -->
    <div class="rules-layout">
      <!-- 左侧规则列表区域 35% -->
      <div class="rules-left">
        <div class="rules-header">
          <h3 class="rules-title">规则列表({{selectedCheckType === 1 ? '美术资源' : selectedCheckType === 2 ? '表格资源':''}})</h3>
          <div class="rules-actions">
            <el-button
              type="text"
              size="mini"
              icon="el-icon-plus"
              @click="handleAddRule"
            >
              新建规则
            </el-button>
          </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
          <div class="filter-item" v-if="selectedCheckType === 1">
            <span class="filter-label">规则分类：</span>
            <el-select
              v-model="filterType"
              placeholder="全部分类"
              size="mini"
              style="width: 120px;"
              @change="handleFilterChange"
            >
              <el-option
                label="全部分类"
                value="all"
              />
              <el-option
                v-for="option in ruleTypeOptions"
                :key="option.type"
                :label="option.type"
                :value="option.type"
              />
            </el-select>
          </div>
           <el-switch
              v-if="isAdmin"
              v-model="batchDeleteMode"
              active-text="批量删除"
              inactive-text=""
              size="small"
              style="margin-left: 8px;"
              @change="handleBatchModeChange"
            />
        </div>

        <!-- 规则表格 -->
        <el-table
          ref="rulesTable"
          :data="rulesList"
          style="width: 100%"
          :header-cell-style="{
            background: '#F7FBFF',
            height: '44px',
          }"
          :row-style="{ height: '40px' }"
          height="calc(100vh - 320px)"
          border
          size="small"
          @selection-change="handleSelectionChange"
        >
          <!-- 多选框列 -->
          <el-table-column
            v-if="batchDeleteMode"
            type="selection"
            width="50"
            align="center"
            :resizable="false"
          />

          <!-- 规则ID -->
          <el-table-column
            prop="ruleId"
            label="ID"
            width="50"
            align="center"
            :resizable="false"
          />

          <!-- 规则名称 -->
          <el-table-column
            prop="ruleName"
            label="规则名称"
            min-width="120"
            align="center"
            show-overflow-tooltip
          />

          <!-- 规则类型 -->
          <el-table-column
            prop="stype"
            label="类型"
            width="80"
            align="center"
            :resizable="false"
          >
            <!-- <template slot-scope="scope">
              <el-tag
                :type="scope.row.ruleType === '表格规则' ? 'primary' : 'success'"
                size="mini"
              >
                {{ scope.row.ruleType === '表格规则' ? '表格' : '美术' }}
              </el-tag>
            </template> -->
          </el-table-column>

          <!-- 创建人 -->
          <el-table-column
            prop="creator"
            label="创建人"
            width="80"
            align="center"
            show-overflow-tooltip
            :resizable="false"
          />

          <!-- 是否启用 -->
          <el-table-column
            label="启用"
            width="55"
            align="center"
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.isEnabled"
                active-color="#13ce66"
                inactive-color="#ff4949"
                class="mini-switch"
                @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            label="操作"
            width="90"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-tooltip content="编辑" placement="top" :open-delay="500">
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-edit"
                    class="action-btn"
                    @click="handleEdit(scope.row)"
                  />
                </el-tooltip>
                <el-tooltip content="置顶" placement="top" :open-delay="500">
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-top"
                    class="action-btn"
                    @click="handleTop(scope.row)"
                  />
                </el-tooltip>
                <el-tooltip content="删除" placement="top" :open-delay="500" v-if="isAdmin">
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-delete"
                    class="action-btn delete-btn"
                    @click="handleDelete(scope.row)"
                  />
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 批量删除按钮 -->
        <div v-if="batchDeleteMode && selectedRules.length > 0" class="batch-actions">
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedRules.length }})
          </el-button>
        </div>

        <!-- 分页器 -->
        <div class="pagination-wrapper">
          <Pagination
            :total="total"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.limit"
            :page-sizes="[10, 20, 50, 100]"
            @pagination="handlePageChange"
          />
        </div>
      </div>

      <!-- 右侧内容区域 65% -->
      <div class="rules-right">
        <!-- 默认状态 -->
        <div v-if="rightPanelType === 'default'" class="placeholder-content">
          <el-empty
            description="请选择左侧操作来查看详细内容"
            :image-size="120"
          />
        </div>

        <!-- 新建规则表单 -->
        <div v-else-if="rightPanelType === 'create'" class="form-content">
          <div class="form-header">
            <h3 class="form-title">{{ getCreateRuleTitle }}</h3>
            <el-button
              type="text"
              icon="el-icon-close"
              class="close-btn"
              @click="closeRightPanel"
            >
              关闭
            </el-button>
          </div>
          <div class="form-body">
            <!-- 根据检查类型显示不同的表单组件 -->
            <CreateRuleForm
              v-if="selectedCheckType === 1"
              ref="createRuleForm"
              @rule-created="handleRuleCreated"
            />
            <TableRuleCreateForm
              v-else-if="selectedCheckType === 2"
              ref="tableRuleCreateForm"
              @rule-created="handleRuleCreated"
            />
            <!-- 默认显示美术资源表单 -->
            <!-- <CreateRuleForm
              v-else
              ref="createRuleForm"
              @rule-created="handleRuleCreated"
            /> -->
          </div>
        </div>

        <!-- 编辑规则表单 -->
        <div v-else-if="rightPanelType === 'edit'" class="form-content">
          <div class="form-header">
            <h3 class="form-title">编辑规则 - {{ currentEditRule.ruleName }}</h3>
            <el-button
              type="text"
              icon="el-icon-close"
              class="close-btn"
              @click="closeRightPanel"
            >
              关闭
            </el-button>
          </div>
          <div class="form-body">
            <!-- 根据规则类型显示不同的编辑表单组件 -->
            <EditRuleForm
              v-if="currentEditRule.ruleType === '美术规则'"
              ref="editRuleForm"
              :rule-data="currentEditRule"
              @rule-updated="handleRuleUpdated"
            />
            <TableRuleEditForm
              v-else-if="currentEditRule.ruleType === '表格规则'"
              ref="tableRuleEditForm"
              :rule-edit-data="currentEditRule"
              @rule-updated="handleRuleUpdated"
            />
            <!-- 默认显示美术资源编辑表单 -->
            <EditRuleForm
              v-else
              ref="editRuleForm"
              :rule-data="currentEditRule"
              @rule-updated="handleRuleUpdated"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getRuleList,updateRuleEnabled,getRuleConfig,deleteRule,getExcelRuleList,topRule } from '@/api/rule'
import { mapGetters,mapState } from 'vuex'
import CreateRuleForm from './rule-create/index.vue'
import EditRuleForm from './rule-edit/index.vue'
import TableRuleCreateForm from './table-rules/rule-create.vue'
import TableRuleEditForm from './table-rules/rule-edit/index.vue'
export default {
  name: 'RulesIndex',
  components: {
    Pagination,
    CreateRuleForm,
    EditRuleForm,
    TableRuleCreateForm,
    TableRuleEditForm
  },
  computed: {
    ...mapGetters([
      'selectedBranch',
      'selectedCheckType'
    ]),
    ...mapState('user', ['isAdmin']),
    // 根据检查类型生成创建规则的标题
    getCreateRuleTitle() {
      if (this.selectedCheckType === 1) {
        return '新建规则(美术资源)'
      } else if (this.selectedCheckType === 2) {
        return '新建规则(表格资源)'
      } else {
        return '新建规则(美术资源)' // 默认显示美术资源
      }
    }
  },
  data() {
    return {
      projectId: '',
      rulesList: [],
      batchDeleteMode: false,
      selectedRules: [],
      filterType: 'all', // 筛选类型，默认为全部
      ruleTypeOptions: [], // 规则类型选项
      total: 0,
      listQuery: {
        page: 1,
        limit: 20000
      },
      // 右侧面板状态管理
      rightPanelType: 'default', // 'default' | 'create' | 'edit'
      currentEditRule: null // 当前编辑的规则数据
    };
  },
  created() {
    // 从路由参数中获取projectId
    this.projectId = this.$route.params.projectId;
    this.fetchRuleConfig(); // 先获取规则配置
    this.fetchRulesList();
    // this.initMockData();
  },
  watch: {
    '$route'(to, from) {
      // 监听路由变化，更新projectId
      if (to.params.projectId !== from.params.projectId) {
        this.projectId = to.params.projectId;
        this.fetchRulesList();
      }
    },
    // 监听选中分支变化
    selectedBranch(newBranch, oldBranch) {
      if (newBranch !== oldBranch && newBranch) {
        this.listQuery.page = 1; // 重置到第一页
        this.fetchRulesList();
        this.fetchRuleConfig();
      }
    },
    // 监听选中检查类型变化
    async selectedCheckType(newType, oldType) {
      if (newType !== oldType && newType) {
        // 如果当前正在创建规则，先清除表单数据
        if (this.rightPanelType === 'create') {
          await this.clearPanelData();
        }

        this.listQuery.page = 1; // 重置到第一页
        // 根据检查类型更新筛选条件
        this.updateFilterByCheckType(newType);
        this.fetchRulesList();
        this.fetchRuleConfig();
      }
    }
  },
  methods: {
   async fetchRulesList() {
      // 根据projectId和筛选条件获取规则列表
      try {
        // 使用 Vuex 中的分支数据，如果没有则使用 sessionStorage 作为备用
        const branch = this.selectedBranch
        if(!branch) return;
        let res;
        // 根据当前选择的检查类型决定使用哪个接口
        if (this.selectedCheckType === 2) {
          // 表格资源 - 使用 getExcelRuleList 接口
          res = await getExcelRuleList({
            projectId: this.projectId,
            branch: branch,
            isExcel: true,
            pageNum: this.listQuery.page,
            pageSize: this.listQuery.limit,
            // stype: this.filterType // 使用当前选择的筛选类型
          });
        } else {
          // 美术资源 - 使用原来的 getRuleList 接口
          res = await getRuleList({
            projectId: this.projectId,
            branch: branch,
            pageNum: this.listQuery.page,
            pageSize: this.listQuery.limit,
            // is_general: false,
            stype: this.filterType // 使用当前选择的筛选条件
          });
        }

        if(res.code === 200){
          // 数据映射，将接口字段映射到表格需要的字段
          if (this.selectedCheckType === 2) {
            // 表格资源的数据映射
            this.rulesList = res.data.map((item) => ({
              ruleId: item.ruleId,
              ruleName: item.ruleName,
              ruleType: '表格规则',
              creator: item.username || '暂无',
              isEnabled: item.isEnabled,
              stype: item.stype,
              rank: item.rank,
              effective_count: item.effective_count
            })) || [];
            this.total = res.count || 0;
          } else {
            // 美术资源的数据映射
            this.rulesList = res.data.map((item) => ({
              ruleId: item.ruleId,
              ruleName: item.ruleName,
              ruleType: item.isTableRule ? '表格规则' : '美术规则',
              creator: item.username || '暂无', // 接口暂无此字段
              isEnabled: item.isEnabled,
              stype: item.stype,
              rank: item.rank,
              effective_count: item.effective_count
            })) || [];
            this.total = res.dataLen || 0;
          }
        } else {
          this.$message.error(res.msg || '获取规则列表失败');
          this.rulesList = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('获取规则列表失败:', error);
        this.$message.error('获取规则列表失败');
        this.rulesList = [];
        this.total = 0;
      }
    },

    // 筛选变化处理
    handleFilterChange() {
      this.listQuery.page = 1; // 重置到第一页
      this.fetchRulesList(); // 重新请求接口，使用新的筛选条件
    },

    // 根据检查类型更新筛选条件
    updateFilterByCheckType(checkType) {
      // 根据检查类型映射到对应的筛选条件
      if (checkType === 1) {
        // 美术资源 - 可以设置特定的筛选条件
        this.filterType = 'all'; // 或者设置为美术相关的类型
      } else if (checkType ===2) {
        // 表格资源 - 可以设置特定的筛选条件
        this.filterType = 'all'; // 或者设置为表格相关的类型
      } else {
        this.filterType = 'all';
      }
    },

    // 获取规则配置
    async fetchRuleConfig() {
      try {
        // 使用 Vuex 中的分支数据，如果没有则使用 sessionStorage 作为备用
        const branch = this.selectedBranch || sessionStorage.getItem('selectedBranch_'+this.projectId) || 'trunk';

        const res = await getRuleConfig({
          projectId: this.projectId,
          branch: branch
        });
        if(res.code === 200 && res.json) {
          // 提取规则类型选项
          this.ruleTypeOptions = res.json.map(item => ({
            type: item.type,
            value: item.value
          }));
        } else {
          this.$message.error(res.msg || '获取规则配置失败');
          this.ruleTypeOptions = [];
        }
      } catch (error) {
        console.error('获取规则配置失败:', error);
        this.$message.error('获取规则配置失败');
        this.ruleTypeOptions = [];
      }
    },

    // 新建规则
    async handleAddRule() {
      if(this.rightPanelType === 'create') return;
      // 清除之前的数据
      await this.clearPanelData();
      this.rightPanelType = 'create';
    },

    // 批量删除模式切换
    handleBatchModeChange(value) {
      this.selectedRules = [];
      if (!value) {
        this.$refs.rulesTable.clearSelection();
      }
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRules = selection;
    },

    // 启用状态变化
    async handleStatusChange(row) {
      try {
        const res = await updateRuleEnabled({
          ruleId: row.ruleId,
          isEnabled: row.isEnabled
        });
        if (res.code === 200) {
          this.$message.success(`规则 ${row.ruleName} 已${row.isEnabled ? '启用' : '禁用'}`);
        } else {
          // 如果接口调用失败，恢复开关状态
          row.isEnabled = !row.isEnabled;
          this.$message.error(res.msg || '更新规则状态失败');
        }
      } catch (error) {
        console.error('更新规则状态失败:', error);
        // 如果接口调用失败，恢复开关状态
        row.isEnabled = !row.isEnabled;
        this.$message.error('更新规则状态失败');
      }
    },

    // 编辑规则
    async handleEdit(row) {
      // 清除之前的数据
      await this.clearPanelData();
      this.currentEditRule = row;
      this.rightPanelType = 'edit';
    },

    // 置顶规则
   async handleTop(row) {
      // TODO: 调用API置顶规则
     const res = await topRule({ rule_id: row.ruleId });
     if (res.code === 200) {
       this.$message.success(`规则 ${row.ruleName} 已置顶`);
      this.fetchRulesList();
     }else{
      this.$message.error(res.msg || '置顶失败,请稍后再试!');
     }
    },

    // 删除规则
    handleDelete(row) {
      this.$confirm(`确定要删除规则 "${row.ruleName}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const res = await deleteRule({ ruleId: row.ruleId });
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.fetchRulesList();
            this.closeRightPanel();
          } else {
            this.$message.error(res.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除规则失败:', error);
          this.$message.error('删除失败');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedRules.length === 0) {
        this.$message.warning('请选择要删除的规则');
        return;
      }

      this.$confirm(`确定要删除选中的 ${this.selectedRules.length} 条规则吗？`, '批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const ruleIds = this.selectedRules.map(item => item.ruleId).join(',');
          const res = await deleteRule({ ruleId: ruleIds });
          if (res.code === 200) {
            this.$message.success(`成功删除 ${this.selectedRules.length} 条规则`);
            this.selectedRules = [];
            this.batchDeleteMode = false;
            this.fetchRulesList();
            this.closeRightPanel()
          } else {
            this.$message.error(res.msg || '批量删除失败');
          }
        } catch (error) {
          console.error('批量删除规则失败:', error);
          this.$message.error('批量删除失败');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 分页变化
    handlePageChange(pagination) {
      this.listQuery.page = pagination.page;
      this.listQuery.limit = pagination.limit;
      this.fetchRulesList(); // 直接重新请求接口
    },

    // 右侧面板控制方法
    async closeRightPanel() {
      // 清除面板数据
      await this.clearPanelData();
      this.rightPanelType = 'default';
      this.currentEditRule = null;
    },

    // 规则创建成功回调
    handleRuleCreated() {
      this.$message.success('规则创建成功');
      // this.closeRightPanel();
      this.fetchRulesList(); // 刷新规则列表
    },

    // 规则更新成功回调
    handleRuleUpdated() {
      this.$message.success('规则更新成功');
      this.closeRightPanel();
      this.fetchRulesList(); // 刷新规则列表
    },

    // 清除面板数据
    async clearPanelData() {
      try {
        // 清除美术资源规则相关的Vuex数据（默认ruleLevel为middle）
        this.$store.commit("conditions/setUploadData", {
          stype: "",
          ruleLevel: "middle",
          isEnabled: true,
          name: "",
          condition: {},
          assert: {},
        });

        // 清除规则配置
        this.$store.commit("conditions/setRuleConfig", []);

        // 清除表格规则相关的Vuex数据（默认ruleLevel为normal）
        this.$store.commit("tableCondition/setUploadData", {
          stype: "",
          ruleLevel: "normal",
          isEnabled: true,
          name: "",
          condition: {},
          assert: {},
          tableHeaders: [],
          calc: [],
          mergeParams: [],
          description: "",
        });

        // 清除表格规则配置
        this.$store.commit("tableCondition/setRuleConfig", []);

        // 等待下一个tick，确保组件已经重新渲染
        await this.$nextTick();

        // // 清除 CreateRuleForm 组件数据
        // if (this.$refs.createRuleForm && typeof this.$refs.createRuleForm.resetFormData === 'function') {
        //   await this.$refs.createRuleForm.resetFormData();
        // }

        // // 清除 TableRuleCreateForm 组件数据
        // if (this.$refs.tableRuleCreateForm && typeof this.$refs.tableRuleCreateForm.resetFormData === 'function') {
        //   await this.$refs.tableRuleCreateForm.resetFormData();
        // }

        // // 清除 EditRuleForm 组件数据
        // if (this.$refs.editRuleForm && typeof this.$refs.editRuleForm.resetFormData === 'function') {
        //   this.$refs.editRuleForm.resetFormData();
        // }

        // // 清除 TableRuleEditForm 组件数据
        // if (this.$refs.tableRuleEditForm && typeof this.$refs.tableRuleEditForm.resetFormData === 'function') {
        //   this.$refs.tableRuleEditForm.resetFormData();
        // }

        console.log('面板数据已清除');
      } catch (error) {
        console.error('清除面板数据失败:', error);
      }
    }
  }
};
</script>

<style scoped lang="scss">
.rules-container {
  padding: 20px;
  height: calc(100vh - 50px);
  background-color: #f5f5f5;
  overflow: auto; // 允许整个容器滚动
}

.rules-layout {
  display: flex;
  height: 100%;
  gap: 10px;
  min-width: 1000px; // 减少最小宽度
}

.rules-left {
  width: 35%; // 减少左侧宽度
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  min-width: 600px; // 减少最小宽度
}

.rules-right {
  width: 65%; // 增加右侧宽度
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
}

.rules-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.rules-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-section {
  padding: 12px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: space-between;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-label {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  font-weight: 500;
}

.el-table {
  flex: 1;
  overflow: auto; // 允许表格滚动

  ::v-deep .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #f7fbff !important;
        font-weight: 600;
        color: #333;
        font-size: 13px;
        padding: 8px 0;
      }
    }
  }

  ::v-deep .el-table__body-wrapper {
    .el-table__row {
      td {
        padding: 8px 0;
        font-size: 12px;
        vertical-align: middle;

        .cell {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 24px;
        }
      }

      &:hover {
        background-color: #f5f7fa;
      }
    }
  }

  // 修复固定列的对齐问题
  ::v-deep .el-table__fixed-right {
    .el-table__fixed-body-wrapper {
      .el-table__row {
        td {
          padding: 8px 0;
          vertical-align: middle;

          .cell {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 24px;
          }
        }
      }
    }
  }

  // 优化滚动条样式
  ::v-deep .el-table__body-wrapper {
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

.batch-actions {
  padding: 12px 20px;
  border-top: 1px solid #e8e8e8;
  background-color: #fafafa;
  text-align: center;
}

.pagination-wrapper {
  padding: 12px 20px;
  border-top: 1px solid #e8e8e8;
  background-color: #fafafa;

  ::v-deep .pagination-container {
    background: transparent;
    padding: 0;
    display: flex;
    justify-content: center;
    margin-top: 0;
  }
}

// 操作按钮样式
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
  height: 100%;
  padding: 0;
}

.action-btn {
  padding: 4px !important;
  margin: 0 !important;
  min-width: 24px;
  height: 24px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgba(64, 158, 255, 0.1);
  }

  &.delete-btn {
    color: #f56c6c;

    &:hover {
      background-color: rgba(245, 108, 108, 0.1);
    }
  }
}

// 迷你开关样式
.mini-switch {
  ::v-deep .el-switch__core {
    width: 28px !important;
    height: 16px !important;
    border-radius: 8px;

    &::after {
      width: 12px !important;
      height: 12px !important;
      border-radius: 6px;
    }
  }

  &.is-checked {
    ::v-deep .el-switch__core::after {
      margin-left: -13px !important;
    }
  }
}

// 筛选下拉框样式优化
::v-deep .filter-section {
  .el-select {
    .el-input__inner {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      font-size: 12px;
      height: 28px;
      line-height: 28px;

      &:hover {
        border-color: #c0c4cc;
      }

      &:focus {
        border-color: #409eff;
      }
    }

    .el-input__suffix {
      .el-select__caret {
        font-size: 12px;
      }
    }
  }
}

.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #999;

  ::v-deep .el-empty {
    .el-empty__description {
      color: #999;
      font-size: 14px;
    }
  }
}

// 右侧表单内容样式
.form-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
  flex-shrink: 0;
}

.form-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  padding: 4px 8px;
  color: #666;

  &:hover {
    color: #409eff;
    background-color: rgba(64, 158, 255, 0.1);
  }
}

.form-body {
  flex: 1;
  overflow: auto;
  padding: 0;
}

// 编辑表单预留样式
.edit-placeholder {
  padding: 20px;

  ::v-deep .el-alert {
    .el-alert__content {
      .el-alert__description {
        p {
          margin: 8px 0;
          line-height: 1.5;

          strong {
            color: #409eff;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .rules-layout {
    flex-direction: column;
    gap: 16px;
  }

  .rules-left,
  .rules-right {
    width: 100%;
  }

  .rules-left {
    height: 60vh;
  }

  .rules-right {
    height: 30vh;
  }
}

// 表格操作按钮样式优化
::v-deep .el-button--text {
  padding: 4px 8px;
  margin: 0 2px;

  &:hover {
    background-color: rgba(64, 158, 255, 0.1);
  }

  &.is-disabled {
    color: #c0c4cc;
  }
}

// Switch开关样式优化
::v-deep .el-switch {
  &.is-checked .el-switch__core {
    background-color: #13ce66;
  }

  .el-switch__core {
    background-color: #ff4949;
  }

  &.el-switch--small {
    .el-switch__core {
      width: 32px;
      height: 18px;
    }

    .el-switch__core::after {
      width: 14px;
      height: 14px;
    }
  }
}

// 标签样式优化
::v-deep .el-tag {
  border-radius: 3px;
  font-size: 11px;
  padding: 0 6px;
  height: 20px;
  line-height: 18px;

  &.el-tag--mini {
    height: 18px;
    line-height: 16px;
    font-size: 10px;
    padding: 0 4px;
  }

  &.el-tag--primary {
    background-color: #ecf5ff;
    border-color: #b3d8ff;
    color: #409eff;
  }

  &.el-tag--success {
    background-color: #f0f9ff;
    border-color: #b3e5fc;
    color: #67c23a;
  }
}
</style>