<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/"
      >
        <img
          v-if="logo"
          :src="projectImageUrl"
          class="sidebar-logo"
          @error="handleImageError"
        />
        <h1 v-else class="sidebar-title">{{ title }}</h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img
          v-if="logo"
          :src="projectImageUrl"
          class="sidebar-logo"
          @error="handleImageError"
        />
        <h1 class="sidebar-title">{{ title }}</h1>
      </router-link>
    </transition>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'

export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  computed: {
    ...mapGetters(['projectName']),
    ...mapState('project', ['projectId']),
    title() {
      // 如果有项目名称就显示项目名称，否则显示默认名称
      return this.projectName || "AssetsLint";
    },
    // 计算项目图片URL
    projectImageUrl() {
      if (this.projectId && !this.imageLoadError) {
        // 使用项目图片
        const baseUrl = process.env.VUE_APP_DEPLOY_TYPE === 'eco'
          ? process.env.VUE_APP_BASE_IMG
          : process.env.VUE_APP_BASE_API;
        return `${baseUrl}/media/sculpture/${this.projectId}.png?time=${Date.now()}`;
      } else {
        // 回退到默认logo
        return require("../../../assets/project_images/logo.png");
      }
    }
  },
  data() {
    return {
      logo: "exist",
      imageLoadError: false, // 图片加载错误标志
    };
  },
  methods: {
    // 处理图片加载错误
    handleImageError() {
      this.imageLoadError = true;
    }
  },
  watch: {
    // 监听项目ID变化，重置错误状态
    projectId() {
      this.imageLoadError = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
      max-width: 150px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
