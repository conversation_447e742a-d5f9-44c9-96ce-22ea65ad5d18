import { login, logout, getInfo } from '@/api/user'
import { getToken, setToken, removeToken, removeLocalStorage } from '@/utils/auth'
import router, { resetRouter } from '@/router'

const state = {
  token: getToken(),
  userName: '',
  name: '',
  avatar: '',
  userId: null,
  introduction: '',
  // roles: [],
  routesMap: [],
  isAdmin: false,
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_USERNAME: (state, userName) => {
    state.userName = userName
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_USER_ID: (state, userId) => {
    state.userId = userId
  },
  // SET_ROLES: (state, roles) => {
  //   state.roles = roles
  // },
  // 角色路由表
  SET_ROUTES_MAP: (state, routesMap) => {
    // state.roles = roles
    state.routesMap = routesMap
  },
  SET_IS_ADMIN: (state, isAdmin) => {
    state.isAdmin = isAdmin
  },
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password }).then(response => {
        const { code, data } = response
        if (code === 200) {
          if (localStorage.getItem('al_userName') !== username.trim()) { //如果换账号登录清空本地保存的数据
            removeLocalStorage()
            const formData = {
              stype: "",
              isEnabled: true,
              name: "",
              conditions: [],
              asserts: [],
            }
            localStorage.setItem("rule_form", JSON.stringify(formData)); //重置创建规则的数据
          }
          commit('SET_TOKEN', data.token)
          setToken(data.token)
        }
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        const { data } = response
        // if (!data) {
        //   reject('验证失败，请重新登录。')
        // }
        // const { roles, name, avatar, introduction } = data
        const { routes, userId, userName, nickName, introduction, roleId } = data
        let avatar = `${process.env.VUE_APP_DEPLOY_TYPE === 'eco' ? process.env.VUE_APP_BASE_IMG : process.env.VUE_APP_BASE_API}/media/avatar/${userId
          }.png?time=${Date.now()}`
        let routesMap = JSON.parse(routes.detail)
        // roles must be a non-empty array
        // if (!roles || roles.length <= 0) {
        //   reject('getInfo: roles must be a non-null array!')
        // }
        if (!routesMap || routesMap.length <= 0) {
          reject('getInfo: routesMap Must be a non-empty array!')
        }

        // commit('SET_ROLES', roles)
        commit('SET_ROUTES_MAP', routesMap)
        commit('SET_USERNAME', userName) //登录名
        commit('SET_NAME', nickName || userName)
        commit('SET_AVATAR', avatar)
        commit('SET_USER_ID', userId)
        commit('SET_INTRODUCTION', introduction)
        commit('SET_IS_ADMIN', roleId === 1? true : false)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout().then(response => {
        const { code, msg } = response
        if (code !== 200) return
        commit('SET_TOKEN', '')
        // commit('SET_ROLES', [])
        commit('SET_ROUTES_MAP', [])
        removeToken()
        resetRouter()
        // reset visited views and cached views
        // dispatch('tagsView/delAllViews', null, { root: true })

        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      // commit('SET_ROLES', [])
      commit('SET_ROUTES_MAP', [])
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    // dispatch('tagsView/delAllViews', null, { root: true })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
