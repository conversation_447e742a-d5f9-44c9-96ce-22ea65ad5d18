<template>
  <el-dialog
    :title="`为【${userInfo ? userInfo.userName : ''}】分配项目`"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="70%"
    @close="handleClose"
  >
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-input
        v-model="searchKeyword"
        placeholder="请输入项目名称搜索"
        clearable
        style="width: 300px; margin-right: 15px"
        @input="handleSearch"
      >
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
    </div>

    <!-- 项目列表表格 -->
    <el-table
      ref="projectTable"
      :data="filteredProjectList"
      style="width: 100%; margin-top: 20px"
      max-height="400"
      :header-cell-style="{
        background: '#F7FBFF',
        height: '52px',
      }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column
        align="center"
        label="项目Logo"
        width="90"
      >
        <template slot-scope="scope">
          <el-image
            style="width: 40px; height: 40px"
            :src="`${baseUrl}/media/sculpture/${scope.row.projectId}.png?time=${currentTime}`"
            fit="cover"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="projectId"
        label="项目ID"
        width="100"
        align="center"
      /> -->
      <el-table-column
        prop="projectName"
        label="项目名称"
        min-width="180"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.projectName }}</span>
          <el-tag
            v-if="isUserOwnedProject(scope.row.projectId)"
            type="success"
            size="mini"
            style="margin-left: 8px"
          >
            已拥有
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="description"
        label="项目描述"
        min-width="200"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="180"
        align="center"
      />
    </el-table>

    <!-- 分页器 -->
    <pagination
      v-show="total > 0"
      style="margin-top: 20px; text-align: center"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="handlePagination"
    />

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <div class="selected-info">
        已选择 <span class="selected-count">{{ selectedProjects.length }}</span> 个项目
      </div>
      <div class="buttons">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :disabled="selectedProjects.length === 0"
          @click="handleConfirm"
        >
          确认分配
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import { getProjectList } from "@/api/permission-project";

export default {
  name: "ProjectAssignDialog",
  components: {
    Pagination,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    userInfo: {
      type: Object,
      default: null,
    },
    userProjects: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      searchKeyword: "",
      projectList: [],
      filteredProjectList: [],
      selectedProjects: [],
      // baseUrl: process.env.VUE_APP_BASE_API,
      baseUrl: process.env.VUE_APP_DEPLOY_TYPE === 'eco' ? process.env.VUE_APP_BASE_IMG : process.env.VUE_APP_BASE_API, //图片基础路径
      currentTime: Date.now(),
      total: 0,
      listQuery: {
        page: 1,
        limit: 10,
      },
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
        if (val) {
          this.resetSelection();
          this.getProjectList();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 获取项目列表
    async getProjectList() {
      try {
        const params = {
          pageNum: this.listQuery.page,
          pageSize: this.listQuery.limit,
        };
        const { code, projectList, dataLen } = await getProjectList(params);
        if (code === 200) {
          this.projectList = projectList || [];
          this.total = dataLen || 0;
          this.handleSearch(); // 应用搜索过滤

          // 等待表格渲染完成后再自动勾选用户已有的项目
          this.$nextTick(() => {
            setTimeout(() => {
              this.autoSelectUserProjects();
            }, 100);
          });
        } else {
          this.$message.error("获取项目列表失败");
        }
      } catch (error) {
        console.error("获取项目列表失败:", error);
        this.$message.error("获取项目列表失败");
      }
    },

    // 搜索过滤
    handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.filteredProjectList = this.projectList;
      } else {
        this.filteredProjectList = this.projectList.filter((project) =>
          project.projectName
            .toLowerCase()
            .includes(this.searchKeyword.toLowerCase())
        );
      }

      // 搜索后重新应用选择状态
      this.$nextTick(() => {
        setTimeout(() => {
          this.autoSelectUserProjects();
          this.restoreSelectedProjects();
        }, 50);
      });
    },

    // 自动勾选用户已有的项目
    autoSelectUserProjects() {
      if (!this.userProjects || this.userProjects.length === 0) {
        return;
      }

      if (!this.filteredProjectList || this.filteredProjectList.length === 0) {
        return;
      }

      if (!this.$refs.projectTable) {
        return;
      }

      // 先清除所有选择
      this.$refs.projectTable.clearSelection();

      // 找到用户已有的项目在当前页面中的对应项
      const userProjectIds = this.userProjects.map(p => p.projectId);
      const projectsToSelect = this.filteredProjectList.filter(project =>
        userProjectIds.includes(project.projectId)
      );

      // 自动勾选这些项目
      if (projectsToSelect.length > 0) {
        projectsToSelect.forEach(project => {
          this.$refs.projectTable.toggleRowSelection(project, true);
        });
      }
    },

    // 恢复已选择的项目状态
    restoreSelectedProjects() {
      if (!this.selectedProjects || this.selectedProjects.length === 0) {
        return;
      }

      // 找到当前已选择的项目在过滤后列表中的对应项
      const selectedProjectIds = this.selectedProjects.map(p => p.projectId);
      const projectsToRestore = this.filteredProjectList.filter(project =>
        selectedProjectIds.includes(project.projectId)
      );

      // 恢复选择状态，但不包括用户原有的项目（避免重复选择）
      const userProjectIds = this.userProjects ? this.userProjects.map(p => p.projectId) : [];
      const projectsToRestoreFiltered = projectsToRestore.filter(project =>
        !userProjectIds.includes(project.projectId)
      );

      if (this.$refs.projectTable && projectsToRestoreFiltered.length > 0) {
        projectsToRestoreFiltered.forEach(project => {
          this.$refs.projectTable.toggleRowSelection(project, true);
        });
      }
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedProjects = selection;
    },

    // 分页处理
    handlePagination() {
      this.getProjectList();
    },

    // 判断用户是否已拥有某个项目
    isUserOwnedProject(projectId) {
      if (!this.userProjects || this.userProjects.length === 0) {
        return false;
      }
      return this.userProjects.some(project => project.projectId === projectId);
    },

    // 重置选择
    resetSelection() {
      this.selectedProjects = [];
      this.searchKeyword = "";
      if (this.$refs.projectTable) {
        this.$refs.projectTable.clearSelection();
      }
    },

    // 确认分配
    handleConfirm() {
      if (this.selectedProjects.length === 0) {
        this.$message.warning("请至少选择一个项目");
        return;
      }
      this.$emit("confirm", this.selectedProjects);
    },

    // 关闭弹窗
    handleClose() {
      this.$emit("update:visible", false);
      this.resetSelection();
    },
  },
};
</script>

<style lang="scss" scoped>
.search-area {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .selected-info {
    color: #606266;
    font-size: 14px;
    
    .selected-count {
      color: #409eff;
      font-weight: bold;
    }
  }
  
  .buttons {
    display: flex;
    gap: 10px;
  }
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}

::v-deep .el-table__body-wrapper {
  &::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background-color: #fff;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #e6e9ed;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #d5d8db;
  }
}
</style>
