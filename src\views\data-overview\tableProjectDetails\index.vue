<template>
  <div class="app-container">
    <div class="pro-detail">
      <div style="display: flex">
        <div>
          <el-descriptions
            :title="'分支详情'"
            :column="2"
            style="font-size: 16px"
          >
            <el-descriptions-item :label="$t('projectDetail.projectName')">{{
              branchStat.project_name
            }}</el-descriptions-item>
            <el-descriptions-item :label="'分支名称'">{{
              branchStat.branch
            }}</el-descriptions-item>
            <el-descriptions-item label="包含的规则数量">
              {{ branchStat.branch_rule_count }}
            </el-descriptions-item>
            <el-descriptions-item label="启用的规则数量">
              {{ branchStat.branch_rule_is_enabled }}
            </el-descriptions-item>
            <el-descriptions-item label="累计检查错误次数">
              {{ branchStat.total_effective_count }}
            </el-descriptions-item>
            <el-descriptions-item label="最后一次全量检查时间">
              {{ branchStat.scan_time }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div>
          <el-divider direction="vertical"></el-divider>
        </div>
        <div v-loading="updateLoading" element-loading-text="更新仓库中...">
          <el-descriptions
            :title="'关联仓库详情'"
            :column="2"
            style="font-size: 16px"
          >
            <el-descriptions-item :span="2" :label="'仓库地址'">{{
              rpeoDetail.url ? rpeoDetail.url : "暂无"
            }}</el-descriptions-item>
            <el-descriptions-item :label="'仓库分支'">{{
              rpeoDetail.repository_branch
                ? rpeoDetail.repository_branch
                : "暂无"
            }}</el-descriptions-item>
            <el-descriptions-item label="仓库版本号">
              {{ rpeoDetail.version ? rpeoDetail.version : "暂无" }}
            </el-descriptions-item>
          </el-descriptions>
          <div v-if="!rpeoDetail.url" style="margin-top: 5px">
            <el-link
              type="primary"
              :underline="false"
              style="margin-right: 20px; font-size: 16px"
              @click="openRepoDialog"
              >关联仓库</el-link
            >
          </div>
          <div v-else style="margin-top: 5px">
            <el-link
              type="primary"
              :underline="false"
              style="margin-right: 20px; font-size: 16px"
              @click="updateRpeo"
              >更新仓库</el-link
            >
            <el-popconfirm title="确定删除吗？" @confirm="deleteRpeo">
              <el-link
                type="danger"
                :underline="false"
                style="font-size: 16px"
                slot="reference"
                >删除仓库</el-link
              >
            </el-popconfirm>
          </div>
        </div>
      </div>
      <el-divider />
      <!-- 关联仓库弹窗 -->
      <el-dialog
        title="关联仓库"
        :visible.sync="repoDialogVisible"
        width="30%"
        @close="closeRepoDialog"
        ><div v-loading="repoLoading" element-loading-text="关联仓库中...">
          <el-form
            :model="repoForm"
            :rules="repoRules"
            ref="repoForm"
            label-width="auto"
            label-position="left"
          >
            <el-form-item label="仓库类型" prop="stype">
              <el-select
                v-model="repoForm.stype"
                placeholder="请选择类型"
                style="width: 150px"
              >
                <el-option label="git" value="git" />
                <el-option label="svn" value="svn" />
              </el-select>
            </el-form-item>
            <el-form-item label="仓库地址" prop="url">
              <el-input
                v-model="repoForm.url"
                placeholder="请输入仓库地址"
              ></el-input>
            </el-form-item>
            <el-form-item label="仓库分支" prop="repository_branch">
              <el-input
                v-model="repoForm.repository_branch"
                placeholder="请输入分支名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="用户名" prop="user">
              <el-input
                v-model="repoForm.user"
                placeholder="请填写仓库登录用户名"
              />
            </el-form-item>
            <el-form-item label="用户密码" prop="password">
              <el-input
                v-model="repoForm.password"
                show-password
                placeholder="请填写仓库登录密码"
              />
            </el-form-item>
          </el-form>
        </div>

        <span slot="footer">
          <el-button type="primary" @click="addRpeo">关 联</el-button>
          <el-button @click="closeRepoDialog">取 消</el-button>
        </span>
      </el-dialog>
      <!-- 检查记录表格 -->
      <!-- <div>
        <h4>{{ "检查记录" }}</h4>
        <div style="margin-bottom: 20px">
          <el-input
            v-model="listQuery.keyword"
            style="width: 15%; margin-right: 5px"
            placeholder="请输入关键字模糊搜索"
            clearable
            @clear="searchResult"
          />
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="searchResult()"
            >搜索</el-button
          >
          <el-button type="primary" icon="el-icon-thumb" @click="fullCheckTable"
            >全量检查</el-button
          >
        </div>
        <el-table :data="checkRecordData" border style="width: 100%">
          <el-table-column prop="check_time" label="检查时间" />
          <el-table-column prop="check_type" label="检查方式" />
          <el-table-column prop="user" label="触发人" />
          <el-table-column prop="status" label="当前状态" />
          <el-table-column prop="result" label="检查结果" />
          <el-table-column
            label="操作"
            fixed="right"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-link
                type="primary"
                :underline="false"
                @click="viewRecord(scope.row)"
                style="margin-right: 15px"
                >查看</el-link
              >
              <el-popconfirm
                title="确定删除吗？"
                @confirm="deleteRecord(scope.row)"
              >
                <el-link type="danger" :underline="false" slot="reference"
                  >删除</el-link
                >
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div> -->

      <div>
        <h4>{{ "检查结果" }}</h4>
        <div style="margin-bottom: 20px">
          <el-input
            v-model="listQuery.keyword"
            style="width: 15%; margin-right: 5px"
            placeholder="请输入关键字模糊搜索"
            clearable
            @clear="searchResult"
          />
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="searchResult()"
            >搜索</el-button
          >
          <el-button type="primary" icon="el-icon-thumb" @click="fullCheckTable"
            >全量检查</el-button
          >
        </div>
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="trigger_time" label="触发时间" />
          <el-table-column prop="rule_name" label="触发规则" />
          <el-table-column prop="trigger_method" label="触发形式" />
          <el-table-column prop="user" label="触发人" />
          <el-table-column prop="branch" label="触发分支" />
          <el-table-column prop="status" label="检查结果">
            <template slot-scope="scope">
              <el-tag
                :type="
                  scope.row.status === '通过'
                    ? 'primary'
                    : scope.row.status === '未通过'
                    ? 'danger'
                    : 'warning'
                "
                >{{ scope.row.status }}</el-tag
              >
              <!-- <el-tag v-if="scope.row.status==='未通过'" type="danger">{{ "未通过" }}</el-tag>
              <el-tag type="warning">{{ "规则编写错误" }}</el-tag> -->
            </template>
          </el-table-column>
          <el-table-column label="错误内容" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.check_result">{{
                scope.row.check_result + "\n"
              }}</span>
              <!-- <span v-if="Object.keys(scope.row.error_report).length !== 0">{{
                JSON.stringify(scope.row.error_report)
              }}</span> -->
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-link
                type="primary"
                :underline="false"
                @click="checkMore(scope.row)"
                >查看更多</el-link
              >
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: end">
          <Pagination
            style="margin-top: 10px; padding: 10px 20px"
            :total="total"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.limit"
            :auto-scroll="listQuery.limit >= 30"
            @pagination="pageSwitch"
          />
        </div>
      </div>
    </div>
    <!-- 查看更多弹窗 -->
    <el-dialog title="检查结果信息" :visible.sync="dialogVisible" width="30%">
      <el-descriptions direction="vertical" :column="1" border>
        <el-descriptions-item v-if="errorInfo.check_result" label="check_result"
          ><span>{{ errorInfo.check_result }}</span></el-descriptions-item
        >
      </el-descriptions>
      <span v-if="!errorInfo.check_result">暂无信息</span>
      <span slot="footer">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!-- 返回顶部组件 -->
    <el-tooltip placement="top" :content="$t('backToTop')">
      <back-to-top
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script>
import BackToTop from "@/components/BackToTop";
import {
  getTableBranchDetail,
  fullScanExcel,
  getTableCheckResult,
  addTableRpeo,
  updateTableRpeo,
  delTableRpeo,
} from "@/api/project";
import Pagination from "@/components/Pagination/index.vue";
import { mapGetters, mapActions } from "vuex";

export default {
  name: "Prodetail",
  components: { Pagination, BackToTop },
  data() {
    return {
      branchStat: {}, // 项目分支详情
      checkRecordData: [], // 检查记录列表
      tableData: [],
      total: 0,
      listQuery: {
        keyword: "",
        page: 1,
        limit: 20,
      },
      dialogVisible: false,
      errorInfo: { exception_result: "", error_report: {} },

      rpeoDetail: {}, //保存关联仓库详情
      repoDialogVisible: false,
      repoLoading: false,
      updateLoading: false,
      repoForm: {
        stype: "git",
        repository_branch: "",
        url: "",
        user: "",
        password: "",
      },
      repoRules: {
        repository_branch: [
          { required: true, message: "请输入分支名称", trigger: "blur" },
        ],
        url: [{ required: true, message: "请输入仓库地址", trigger: "blur" }],
        user: [{ required: true, message: "请输入用户名", trigger: "blur" }],
        password: [
          { required: true, message: "请输入用户密码", trigger: "blur" },
        ],
      },

      // reportList: [],
      // reportListTotal: 0,
    };
  },
  // 在页面离开时记录滚动位置
  beforeRouteLeave(to, from, next) {
    this.scrollTop =
      document.documentElement.scrollTop || document.body.scrollTop;
    next();
  },
  // 进入该页面时，用之前保存的滚动位置赋值
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      document.body.scrollTop = vm.scrollTop;
    });
  },

  mounted() {
    this.initializeData();
  },
  watch: {
    // 监听路由变化，更新项目ID
    '$route'(to, from) {
      if (to.params.projectId !== from.params.projectId) {
        console.log('项目ID变化，重新获取数据:', to.params.projectId);
        this.initializeData();
      }
    },
    // 监听选中分支变化
    selectedBranch(newBranch, oldBranch) {
      if (newBranch !== oldBranch && newBranch && this.currentProjectId) {
        console.log('分支变化，重新获取数据:', newBranch);
        this.refreshData();
      }
    },
    // 监听选中检查类型变化
    selectedCheckType(newType, oldType) {
      if (newType !== oldType && newType && this.currentProjectId && this.selectedBranch) {
        console.log('检查类型变化，重新获取数据:', newType);
        this.refreshData();
      }
    }
  },
  computed: {
    ...mapGetters(["projectName", "projectBranch", "selectedBranch", "selectedCheckType"]),
    // 获取当前项目ID
    currentProjectId() {
      return this.$route.params.projectId || sessionStorage.getItem('currentProjectId')
    }
  },
  methods: {
    ...mapActions('project', ['fetchBranchOptions']),

    // 初始化数据
    async initializeData() {
      if (!this.currentProjectId) {
        console.warn('未找到项目ID');
        return;
      }
      // 获取报告数据
      this.refreshData();
    },

    // 刷新数据
    refreshData() {
      this.getBranchDetail();
      // this.getCheckRecordList();
      this.geCheckResultList();
    },

    // 获取分支详情数据
    async getBranchDetail() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        return;
      }

      const params = {
        project_id: projectId,
        branch: branch,
      };
      const { code, data } = await getTableBranchDetail(params);
      if (code === 200) {
        this.branchStat = data;
        this.rpeoDetail = data.repository;
      }
    },

    // 全量检查表格
    async fullCheckTable() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        return;
      }

      const params = {
        project_id: projectId,
        branch: branch,
      };
      const { code, msg } = await fullScanExcel(params);
      if (code === 200) {
        this.$message.success(msg);
        this.refreshData()
      }
    },

    // 获取检查记录列表数据
    async getCheckRecordList() {
      // mockdata
      const data = [
        {
          id: 1,
          check_time: "2024-08-03 12:00:00",
          check_type: "全量检查",
          user: "用户1",
          status: "检查中",
          result: "通过",
        },
        {
          id: 2,
          check_time: "2024-08-03 12:00:00",
          check_type: "全量检查",
          user: "用户1",
          status: "已完成",
          result: "未通过",
        },
      ];
      // const params = {
      //   project_id: this.$route.query.projectId,
      //   branch,
      //   search: this.listQuery.keyword,
      //   pageNum: this.listQuery.page,
      //   pageSize: this.listQuery.limit,
      // };
      // const { code, msg, data, total_count } = await getTableCheckResult(
      //   params
      // );
      // if (code !== 200) return this.$message.warning(msg);
      this.checkRecordData = data;
      this.total = data.length;
    },
    // 查看检查记录
    async viewRecord(row) {
      console.log(row);
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      const params = {
        project_id: projectId,
        branch: branch,
        check_id: row.id,
      };
      this.$router.push({
        path: "/tabledetail/checkdetail",
        query: params,
      });
    },
    deleteRecord(row) {
      console.log(row);
    },

    // 获取检查结果列表
    async geCheckResultList() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        this.goBack();
        return;
      }

      // mock data
      // const data = [
      //   {
      //     time: "2024-07-03 12:00:00",
      //     rule: "规则1",
      //     type: "类型1",
      //     user: "用户1",
      //     branch: "分支1",
      //     error:
      //       "错误内容中只显示信息的部分内容后续使用省略号代替，点击查看更多时再弹出二级界面显示所有的详细的错误信息",
      //   },
      //   {
      //     time: "2024-07-03 12:00:00",
      //     rule: "规则2",
      //     type: "类型2",
      //     user: "用户2",
      //     branch: "分支2",
      //     error: "错误2",
      //   },
      // ];
      // this.tableData = data;
      // this.total = data.length;
      const params = {
        project_id: projectId,
        branch: branch,
        search: this.listQuery.keyword,
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.limit,
      };
      const { code, msg, data, total_count } = await getTableCheckResult(
        params
      );
      if (code !== 200) return this.$message.warning("检查结果" + msg);
      this.tableData = data;
      this.total = total_count ? total_count : 0;
    },

    // 分页器页码切换 获取页码和页数
    pageSwitch(val) {
      this.geCheckResultList();
    },
    searchResult() {
      this.listQuery.page = 1;
      this.listQuery.limit = 20;
      // console.log("搜索", this.listQuery);
      this.geCheckResultList();
    },
    // 查看更多详情页
    checkMore(row) {
      // console.log(row);
      this.errorInfo = row;
      this.dialogVisible = true;
    },

    // 打开关联仓库弹窗
    openRepoDialog() {
      this.repoForm.branch = this.selectedBranch || this.$route.query.branch;
      this.repoDialogVisible = true;
    },
    // 关闭关联仓库弹窗
    closeRepoDialog() {
      this.$refs.repoForm.resetFields();
      this.repoDialogVisible = false;
    },
    // 获取关联仓库详情
    async getRpeoDetail() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      const params = {
        project_id: projectId,
        branch: branch,
      };
      // const { code, data } = await getRpeoDetail(params);
      // if (code === 200) {
      //   this.rpeoDetail = data;
      // }
      const data = {
        // url: "xxxxxxxxxxxxxxxxxxxxxxxxxx",
        // branch: "master",
        // version: "1.0.0",
      };
      this.rpeoDetail = data;
    },
    // 关联仓库
    addRpeo() {
      this.$refs.repoForm.validate((valid) => {
        if (valid) {
          const projectId = this.currentProjectId;
          const branch = this.selectedBranch || this.$route.query.branch;

          const params = {
            project_id: projectId,
            branch: branch,
            ...this.repoForm,
          };
          // const { code, msg } = addTableGit(params);
          this.repoLoading = true;
          addTableRpeo(params).then(({ code, msg }) => {
            if (code === 200) {
              this.$message.success(msg);
              this.repoLoading = false;
              this.getBranchDetail();
            }
            this.closeRepoDialog();
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 更新仓库
    async updateRpeo() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      const params = {
        project_id: projectId,
        branch: branch,
        stype: this.repoForm.stype,
      };
      this.updateLoading = true;
      const { code, msg } = await updateTableRpeo(params);
      if (msg) this.updateLoading = false;
      if (code !== 200) return this.$message.warning(msg);
      this.$message.success(msg);
      this.getBranchDetail();
    },
    // 删除仓库
    async deleteRpeo() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      const params = {
        project_id: projectId,
        branch: branch,
      };
      const { code, msg } = await delTableRpeo(params);
      if (code === 200) {
        this.$message.success(msg);
        this.getBranchDetail();
      }
    },

    goBack() {
      // this.$router.push("/tabledetect/project");
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  .pro-detail {
    background-color: #fff;
    padding: 15px;
    padding-top: 25px;
    :deep(.el-table) {
      flex: 1;
    }
  }
}
.el-divider--vertical {
  height: 100%;
  margin: 0 80px;
}
::v-deep .el-descriptions-item__content {
  white-space: pre-wrap;
}
</style>
