<template>
  <div class="art-branch-overview">
    <div class="overview-stats">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-document"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ total }}</div>
            <div class="stat-label">总报告数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon success">
            <i class="el-icon-success"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ successCount }}</div>
            <div class="stat-label">通过报告</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon error">
            <i class="el-icon-error"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ errorCount }}</div>
            <div class="stat-label">异常报告</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon warning">
            <i class="el-icon-warning"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ warningCount }}</div>
            <div class="stat-label">警告报告</div>
          </div>
        </div>
      </div>
    </div>

    <div class="overview-charts">
      <div class="chart-section">
        <h4 class="chart-title">检查结果分布</h4>
        <div class="chart-placeholder">
          <el-empty 
            description="图表功能开发中..." 
            :image-size="80"
          />
        </div>
      </div>
    </div>

    <div class="overview-recent">
      <h4 class="section-title">最近检查记录</h4>
      <div class="recent-list">
        <div 
          v-for="report in recentReports" 
          :key="report.id"
          class="recent-item"
        >
          <div class="recent-info">
            <div class="recent-time">{{ formatTime(report.scan_time) }}</div>
            <div class="recent-result">
              <el-tag 
                :type="report.passTotal !== report.count ? 'danger' : 'success'"
                size="mini"
              >
                {{ report.passTotal }}/{{ report.count }}
              </el-tag>
            </div>
          </div>
          <div class="recent-trigger">{{ report.tigger_type }}</div>
        </div>
        <div v-if="recentReports.length === 0" class="no-data">
          暂无检查记录
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ArtBranchOverview",
  props: {
    projectId: {
      type: String,
      required: true
    },
    branch: {
      type: String,
      required: true
    },
    reportsList: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    }
  },
  computed: {
    successCount() {
      return this.reportsList.filter(report => 
        report.passTotal === report.count
      ).length;
    },
    errorCount() {
      return this.reportsList.filter(report => 
        report.passTotal !== report.count
      ).length;
    },
    warningCount() {
      // 预留警告计数逻辑
      return 0;
    },
    recentReports() {
      // 显示最近5条记录
      return this.reportsList.slice(0, 5);
    }
  },
  methods: {
    formatTime(timeStr) {
      if (!timeStr) return "";
      const date = new Date(timeStr);
      return date.toLocaleString("zh-CN", {
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    }
  }
};
</script>

<style scoped lang="scss">
.art-branch-overview {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.overview-stats {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #666;
  font-size: 18px;

  &.success {
    background-color: #f0f9ff;
    color: #67c23a;
  }

  &.error {
    background-color: #fef0f0;
    color: #f56c6c;
  }

  &.warning {
    background-color: #fdf6ec;
    color: #e6a23c;
  }
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 13px;
  color: #666;
  margin-top: 4px;
}

.overview-charts {
  margin-bottom: 24px;
}

.chart-section {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
}

.chart-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overview-recent {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.recent-list {
  max-height: 300px;
  overflow-y: auto;
}

.recent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.recent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.recent-time {
  font-size: 13px;
  color: #666;
}

.recent-trigger {
  font-size: 13px;
  color: #999;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
  font-size: 14px;
}
</style>
