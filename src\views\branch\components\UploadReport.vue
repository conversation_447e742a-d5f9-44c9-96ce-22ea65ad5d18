<template>
  <div>
    <el-button type="success" icon="el-icon-upload" @click="uploadFiles">{{
      $t("project.uploadReport")
    }}</el-button>
    <!-- 上传弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :visible.sync="dialogUpload"
      :close-on-click-modal="false"
      :title="$t('project.uploadReport')"
      width="35%"
      append-to-body
      @close="closeUpload"
    >
      <div style="text-align: center">
        <!-- 此处action后续需改为有效的接口——当前为组件的官网范例接口 -->
        <el-upload
          style="margin-bottom: 20px"
          ref="uploadFile"
          drag
          action=""
          accept=""
          :on-change="fileChange"
          :on-remove="fileRemove"
          :auto-upload="false"
          :multiple="true"
          :file-list="uploadFilesList"
          :show-file-list="false"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            {{ $t("project.uploadTips1")
            }}<em>{{ $t("project.uploadTips2") }}</em>
          </div>
        </el-upload>
        <el-table
          v-loading="loading"
          :element-loading-text="$t('project.uploading')"
          :data="uploadFilesList"
          max-height="300"
          :show-header="false"
        >
          <el-table-column
            prop="name"
            :show-overflow-tooltip="true"
            label="File Name"
          >
            <template slot-scope="scope">
              <i style="color: #409eff" class="el-icon-s-order" />
              {{ scope.row.name }}
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            width="100"
            fixed="right"
            align="center"
            :show-overflow-tooltip="true"
            label="Operate"
          >
            <template slot-scope="scope">
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                size="mini"
                @click.native.prevent="delFile(scope.$index)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer">
        <el-button size="mini" type="primary" @click="uploadConfirm">{{
          $t("project.upload")
        }}</el-button>
        <el-button size="mini" @click="closeUpload">{{
          $t("project.close")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import { uploadReport } from "@/api/upload";

export default {
  props: ["projectId"],
  name: "UploadReport",
  directives: { elDragDialog },
  data() {
    return {
      uploadFilesList: [], // 上传文件回显列表
      dialogUpload: false, // 上传弹窗开关
      loading: false, //上传加载动画
    };
  },
  mounted() {},
  methods: {
    // 点击上传文件
    uploadFile() {
      this.dialogUpload = true;
      this.$nextTick(() => {
        document.getElementsByClassName(
          "el-upload__input"
        )[0].webkitdirectory = false;
      });
    },
    // 点击上传文件夹
    uploadFiles() {
      this.dialogUpload = true;

      // this.$nextTick(() => {
      // 以下代码找input，再给加webkitdirectory 属性实现上传目录
      // 方式1
      // this.$refs.uploadFile.$children[0].$refs.input.webkitdirectory = true;
      // 方式2
      // document.getElementsByClassName(
      //   "el-upload__input"
      // )[0].webkitdirectory = true;
      // });
    },

    // 上传文件之前
    beforeUpload(file) {
      this.uploadFilesList.forEach((item) => {
        if (isEquael(item.fileName, file.name)) {
          return this.$message.warning(this.$t("project.fileExists"));
        }
      });
    },
    // 文件上传时的钩子
    fileChange(file, fileList) {
      // 判断文件名是否重复
      let count = 0;
      fileList.forEach((item, idx) => {
        // 移除不是表格格式的文件
        // if (
        //   item.raw.type !== "text/csv" &&
        //   item.raw.type !==
        //     "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        // ) {
        //   // console.log(item);
        //   fileList.splice(idx, 1);
        // }
        // 在此处，对比文件名，将文件名相同的对比次数累加，
        // 相同的文件名累加值为 2 时，说明文件名已经重复，直接删掉。

        if (file.name == item.name) {
          count++;
          if (count === 2) {
            setTimeout(() => {
              this.$message({
                message: file.name + this.$t("project.existed"),
                type: "info",
              });
            }, 10);
            fileList.pop(); // 相同则删除新增进来的文件
          }
        }
      });

      this.uploadFilesList = fileList;
    },

    fileRemove(file, fileList, name) {
      this.uploadFilesList = fileList;
    },
    delFile(index) {
      this.uploadFilesList.splice(index, 1);
    },

    closeUpload() {
      this.uploadFilesList = [];
      this.loading = false;
      this.dialogUpload = false;
    },

    // 点击上传真正的服务器
    async uploadConfirm() {
      // 判断是否有文件再上传
      if (this.uploadFilesList.length === 0) {
        return this.$message.warning(this.$t("project.selectFiles"));
      }
      // 下面的代码将创建一个空的FormData对象:
      const formData = new FormData();
      // 使用FormData.append来添加键/值对到表单里面；
      this.uploadFilesList.forEach((file) => {
        formData.append("files", file.raw);
      });
      // 添加自定义参数
      formData.append("projectId", this.projectId);
      // formData.append("is_res", true); //仅测试传值,正式需后端上传原始资源
      if (!this.loading) {
        this.loading = true;
        let { code, msg } = await uploadReport(formData);
        if (code === 200) {
        //   this.$parent.getProjectBranchs();
        this.$emit('refresh')
          this.$message.success(this.$t("project.uploadSuccess"));
          this.loading = false;
          this.closeUpload();
          return;
        }
        if (code === 306) {
          this.$message.error(this.$t("project.wrongFormat"));
          this.uploadFilesList = [];
          this.loading = false;
          return;
        } else {
          this.$message.error(msg);
          this.loading = false;
        }
        // if (code === 302) {
        //   this.$message.error(msg);
        //   this.uploadFilesList = [];
        //   this.loading = false;
        //   return;
        // }
        // if (code === 304) {
        //   this.$message.error(msg);
        //   this.uploadFilesList = [];
        //   this.loading = false;
        // }
      } else {
        this.$message.warning(this.$t("project.uploading"));
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@media (max-width: 1130px) {
  ::v-deep .el-upload-dragger {
    width: 200px;
  }
}
@media (max-width: 675px) {
  ::v-deep .el-upload-dragger {
    width: 150px;
  }
}
</style>
